import { useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from '@/styles/ServicesHeroShowcase.module.css';
import AnimatedSection from './AnimatedSection';
import { safeRender } from '@/lib/safe-render-utils';

/**
 * Debug version of ServicesHeroShowcase to isolate the "Element type is invalid" error
 */
const ServicesHeroShowcaseDebug = ({
  title = 'Our Services',
  subtitle = 'Transforming moments into memories with magic and creativity',
  backgroundImage = '/background.png',
  services = [],
  ctaText = 'Book Now',
  ctaLink = '/book-online',
  ...props
}) => {
  console.log('ServicesHeroShowcaseDebug - Props:', {
    title: typeof title,
    subtitle: typeof subtitle,
    backgroundImage: typeof backgroundImage,
    services: Array.isArray(services),
    ctaText: typeof ctaText,
    ctaLink: typeof ctaLink
  });

  console.log('ServicesHeroShowcaseDebug - Services data:', services);

  const showcaseRef = useRef(null);
  const floatingCirclesRef = useRef(null);

  // Default services if none provided
  const defaultServices = [
    {
      title: 'Face Painting',
      image: '/images/services/face-painting.jpg',
      icon: '🎨',
      color: '#4ECDC4'
    }
  ];

  const showcaseServices = (services && Array.isArray(services) && services.length > 0) ? services : defaultServices;

  console.log('ServicesHeroShowcaseDebug - Showcase services:', showcaseServices);

  // Test each service property
  showcaseServices.forEach((service, index) => {
    console.log(`Service ${index}:`, {
      title: typeof service?.title,
      image: typeof service?.image,
      icon: typeof service?.icon,
      color: typeof service?.color,
      safeTitle: safeRender(service?.title),
      safeImage: safeRender(service?.image),
      safeIcon: safeRender(service?.icon),
      safeColor: safeRender(service?.color)
    });
  });

  return (
    <section
      ref={showcaseRef}
      className={styles.servicesShowcase}
      style={{ backgroundImage: `url(${backgroundImage})` }}
      {...props}
    >
      <div className={styles.overlayGradient}></div>

      {/* Test floating circles */}
      <div ref={floatingCirclesRef} className={styles.floatingCircles}>
        {showcaseServices.map((service, index) => (
          <div
            key={index}
            className={styles.floatingCircle}
            style={{
              backgroundColor: 'transparent',
              animationDelay: `${index * 0.3}s`
            }}
          >
            <div className={styles.circleImageContainer}>
              {safeRender(service?.image) ? (
                <Image
                  src={safeRender(service?.image, '/images/services/face-paint.jpg')}
                  alt={safeRender(service?.title, 'Service')}
                  width={150}
                  height={150}
                  className={styles.circleImage}
                />
              ) : (
                <span className={styles.circleIcon}>{safeRender(service?.icon, '🎨')}</span>
              )}
            </div>
            <div className={styles.circleOverlay} style={{ backgroundColor: safeRender(service?.color, '#4ECDC4') }}></div>
          </div>
        ))}
      </div>

      {/* Test with AnimatedSection */}
      <div className={styles.showcaseContent}>
        <AnimatedSection animation="fade-in" delay={100}>
          <h1 className={styles.showcaseTitle}>{title}</h1>
          <p className={styles.showcaseSubtitle}>{subtitle}</p>

          <div className={styles.showcaseCta}>
            <Link href={ctaLink} className="button button--glow">
              {ctaText}
            </Link>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
};

export default ServicesHeroShowcaseDebug;
