#!/usr/bin/env node

/**
 * Comprehensive Element Type Error Test
 * Ocean Soul Sparkles - Final Verification
 */

import fs from 'fs';
import { execSync } from 'child_process';

console.log('🔍 COMPREHENSIVE "Element type is invalid" ERROR TEST');
console.log('=' .repeat(70));

const results = {
  frameworkChecks: { passed: 0, failed: 0, issues: [] },
  componentChecks: { passed: 0, failed: 0, issues: [] },
  buildChecks: { passed: 0, failed: 0, issues: [] }
};

function addResult(category, check, passed, details = '') {
  results[category].passed += passed ? 1 : 0;
  results[category].failed += passed ? 0 : 1;
  
  if (!passed) {
    results[category].issues.push({ check, details });
  }
  
  console.log(`   ${passed ? '✅' : '❌'} ${check}${details ? ` - ${details}` : ''}`);
}

// Framework Verification
console.log('\n📋 PHASE 1: FRAMEWORK VERIFICATION');
console.log('-'.repeat(50));

try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // Check React version
  const reactVersion = packageJson.dependencies.react;
  const isReactValid = reactVersion && reactVersion.includes('18.');
  addResult('frameworkChecks', 'React 18.x installed', isReactValid, 
    isReactValid ? `(${reactVersion})` : `Found: ${reactVersion}`);
  
  // Check Next.js version
  const nextVersion = packageJson.dependencies.next;
  const isNextValid = nextVersion && nextVersion.includes('14.');
  addResult('frameworkChecks', 'Next.js 14.x installed', isNextValid,
    isNextValid ? `(${nextVersion})` : `Found: ${nextVersion}`);
  
  // Check if type is module
  const isModule = packageJson.type === 'module';
  addResult('frameworkChecks', 'Package type is module', isModule,
    isModule ? '' : `Found: ${packageJson.type || 'commonjs'}`);
  
} catch (error) {
  addResult('frameworkChecks', 'Package.json readable', false, error.message);
}

// Next.js Configuration
try {
  const nextConfig = fs.readFileSync('next.config.js', 'utf8');
  const hasReactStrictMode = nextConfig.includes('reactStrictMode: true');
  addResult('frameworkChecks', 'React Strict Mode enabled', hasReactStrictMode);
  
  const hasSwcMinify = nextConfig.includes('swcMinify: true');
  addResult('frameworkChecks', 'SWC minification enabled', hasSwcMinify);
  
} catch (error) {
  addResult('frameworkChecks', 'Next.js config readable', false, error.message);
}

// Babel Configuration
try {
  const babelConfig = JSON.parse(fs.readFileSync('babel.config.json', 'utf8'));
  const hasReactPreset = babelConfig.presets?.some(preset => 
    Array.isArray(preset) ? preset[0].includes('react') : preset.includes('react')
  );
  addResult('frameworkChecks', 'Babel React preset configured', hasReactPreset);
  
} catch (error) {
  addResult('frameworkChecks', 'Babel config readable', false, error.message);
}

// Component Verification
console.log('\n🔧 PHASE 2: COMPONENT VERIFICATION');
console.log('-'.repeat(50));

try {
  const heroShowcase = fs.readFileSync('components/ServicesHeroShowcase.js', 'utf8');
  
  // Check imports
  const hasSafeRenderImport = heroShowcase.includes("import { safeRender } from '@/lib/safe-render-utils'");
  addResult('componentChecks', 'safeRender import present', hasSafeRenderImport);
  
  const hasReactImport = heroShowcase.includes("import { useEffect, useRef } from 'react'");
  addResult('componentChecks', 'React hooks import present', hasReactImport);
  
  // Check safe array handling
  const hasSafeArrayCheck = heroShowcase.includes('(services && Array.isArray(services) && services.length > 0)');
  addResult('componentChecks', 'Safe array check implemented', hasSafeArrayCheck);
  
  // Check safe property rendering
  const hasSafeImageRender = heroShowcase.includes('safeRender(service?.image');
  addResult('componentChecks', 'Safe image rendering', hasSafeImageRender);
  
  const hasSafeTitleRender = heroShowcase.includes('safeRender(service?.title');
  addResult('componentChecks', 'Safe title rendering', hasSafeTitleRender);
  
  const hasSafeIconRender = heroShowcase.includes('safeRender(service?.icon');
  addResult('componentChecks', 'Safe icon rendering', hasSafeIconRender);
  
  const hasSafeColorRender = heroShowcase.includes('safeRender(service?.color');
  addResult('componentChecks', 'Safe color rendering', hasSafeColorRender);
  
  // Check for unsafe patterns
  const hasUnsafeImageRender = heroShowcase.includes('src={service.image}');
  addResult('componentChecks', 'No unsafe image rendering', !hasUnsafeImageRender);
  
  const hasUnsafeTitleRender = heroShowcase.includes('alt={service.title}') && 
                               !heroShowcase.includes('safeRender(service?.title');
  addResult('componentChecks', 'No unsafe title rendering', !hasUnsafeTitleRender);
  
  const hasUnsafeColorRender = heroShowcase.includes('backgroundColor: service.color') &&
                               !heroShowcase.includes('safeRender(service?.color');
  addResult('componentChecks', 'No unsafe color rendering', !hasUnsafeColorRender);
  
} catch (error) {
  addResult('componentChecks', 'ServicesHeroShowcase readable', false, error.message);
}

// Safe Render Utils Verification
try {
  const safeRenderUtils = fs.readFileSync('lib/safe-render-utils.js', 'utf8');
  
  const hasReactImport = safeRenderUtils.includes("import React from 'react'");
  addResult('componentChecks', 'safe-render-utils has React import', hasReactImport);
  
  const hasSafeRenderFunction = safeRenderUtils.includes('export const safeRender');
  addResult('componentChecks', 'safeRender function exported', hasSafeRenderFunction);
  
} catch (error) {
  addResult('componentChecks', 'safe-render-utils readable', false, error.message);
}

// Build Verification
console.log('\n🏗️ PHASE 3: BUILD VERIFICATION');
console.log('-'.repeat(50));

try {
  console.log('   🔄 Testing Next.js compilation...');
  const buildOutput = execSync('npm run build 2>&1', { 
    encoding: 'utf8', 
    timeout: 60000,
    cwd: process.cwd()
  });
  
  const buildSuccess = buildOutput.includes('✓ Compiled successfully');
  addResult('buildChecks', 'Next.js build successful', buildSuccess);
  
  const noTypeErrors = !buildOutput.includes('Type error');
  addResult('buildChecks', 'No TypeScript errors', noTypeErrors);
  
  const noReactErrors = !buildOutput.includes('Element type is invalid');
  addResult('buildChecks', 'No React element type errors', noReactErrors);
  
} catch (error) {
  addResult('buildChecks', 'Build process completed', false, error.message);
}

// Summary
console.log('\n' + '=' .repeat(70));
console.log('📊 COMPREHENSIVE TEST SUMMARY');
console.log('=' .repeat(70));

const categories = ['frameworkChecks', 'componentChecks', 'buildChecks'];
const categoryNames = ['Framework', 'Component', 'Build'];

categories.forEach((category, index) => {
  const { passed, failed, issues } = results[category];
  const total = passed + failed;
  const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : '0.0';
  
  console.log(`\n${categoryNames[index]} Verification:`);
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Success Rate: ${successRate}%`);
  
  if (issues.length > 0) {
    console.log(`   ⚠️  Issues:`);
    issues.forEach((issue, idx) => {
      console.log(`      ${idx + 1}. ${issue.check}: ${issue.details}`);
    });
  }
});

const totalPassed = categories.reduce((sum, cat) => sum + results[cat].passed, 0);
const totalFailed = categories.reduce((sum, cat) => sum + results[cat].failed, 0);
const overallSuccess = totalPassed + totalFailed > 0 ? 
  ((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1) : '0.0';

console.log(`\n🎯 OVERALL SUCCESS RATE: ${overallSuccess}%`);

if (totalFailed === 0) {
  console.log('\n🎉 ALL TESTS PASSED!');
  console.log('✅ Framework setup is correct');
  console.log('✅ Component implementation is safe');
  console.log('✅ Build process is successful');
  console.log('✅ "Element type is invalid" error should be resolved');
  console.log('\n🚀 Services page should now load without React errors!');
} else {
  console.log('\n⚠️  ISSUES DETECTED - Manual verification required');
  console.log('🔧 Please address the failed checks above');
  console.log('🌐 Test the services page manually in browser');
}

console.log('\n🔍 Test complete!');
