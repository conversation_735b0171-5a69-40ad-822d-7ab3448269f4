#!/usr/bin/env node

/**
 * Comprehensive React Error #130 Resolution Verification Script
 * Ocean Soul Sparkles - Services Page Error Resolution
 *
 * This script verifies that all React Error #130 issues have been resolved
 * by checking the actual implementation against known error patterns.
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 Comprehensive React Error #130 Resolution Verification');
console.log('=' .repeat(70));

const results = {
  totalChecks: 0,
  passedChecks: 0,
  failedChecks: 0,
  issues: []
};

function addResult(check, passed, details = '') {
  results.totalChecks++;
  if (passed) {
    results.passedChecks++;
    console.log(`   ✅ ${check}`);
  } else {
    results.failedChecks++;
    results.issues.push({ check, details });
    console.log(`   ❌ ${check} - ${details}`);
  }
}

// Check 1: Verify safe-render-utils.js has React import
console.log('\n1. Checking safe-render-utils.js React import:');
try {
  const safeRenderUtils = fs.readFileSync('lib/safe-render-utils.js', 'utf8');
  const hasReactImport = safeRenderUtils.includes("import React from 'react'");
  addResult('React import present in safe-render-utils.js', hasReactImport,
    hasReactImport ? '' : 'Missing React import for isValidElement check');
} catch (error) {
  addResult('safe-render-utils.js file exists', false, error.message);
}

// Check 2: Verify services.js has safe heroServices mapping
console.log('\n2. Checking services.js heroServices mapping:');
try {
  const servicesPage = fs.readFileSync('pages/services.js', 'utf8');

  const hasSafeHeroMapping = servicesPage.includes('(services && Array.isArray(services) ? services : []).map(service => ({');
  addResult('Safe heroServices array mapping', hasSafeHeroMapping,
    hasSafeHeroMapping ? '' : 'heroServices mapping not using safe array checks');

  const hasSafeRenderInHero = servicesPage.includes('safeRender(service?.title)') &&
                              servicesPage.includes('safeRender(service?.icon');
  addResult('Safe rendering in heroServices mapping', hasSafeRenderInHero,
    hasSafeRenderInHero ? '' : 'heroServices mapping not using safeRender()');

  const hasSafeMainMapping = servicesPage.includes('{(services && Array.isArray(services) ? services : []).map((service') ||
                             servicesPage.includes('(services && Array.isArray(services) ? services : []).map((service');
  addResult('Safe main services array mapping', hasSafeMainMapping,
    hasSafeMainMapping ? '' : 'Main services mapping not using safe array checks');

  const hasSafeKeyProp = servicesPage.includes('key={safeRender(service?.id');
  addResult('Safe key prop in service mapping', hasSafeKeyProp,
    hasSafeKeyProp ? '' : 'Service key prop not using safeRender()');

} catch (error) {
  addResult('services.js file exists', false, error.message);
}

// Check 3: Verify ServicesHeroShowcase.js has safe rendering
console.log('\n3. Checking ServicesHeroShowcase.js safe rendering:');
try {
  const heroShowcase = fs.readFileSync('components/ServicesHeroShowcase.js', 'utf8');

  const hasSafeRenderImport = heroShowcase.includes("import { safeRender } from '@/lib/safe-render-utils'");
  addResult('safeRender import in ServicesHeroShowcase', hasSafeRenderImport,
    hasSafeRenderImport ? '' : 'Missing safeRender import');

  const hasSafeImageRender = heroShowcase.includes('safeRender(service?.image');
  addResult('Safe image rendering in hero showcase', hasSafeImageRender,
    hasSafeImageRender ? '' : 'Image not using safeRender()');

  const hasSafeTitleRender = heroShowcase.includes('safeRender(service?.title');
  addResult('Safe title rendering in hero showcase', hasSafeTitleRender,
    hasSafeTitleRender ? '' : 'Title not using safeRender()');

  const hasSafeColorRender = heroShowcase.includes('safeRender(service?.color');
  addResult('Safe color rendering in hero showcase', hasSafeColorRender,
    hasSafeColorRender ? '' : 'Color not using safeRender()');

} catch (error) {
  addResult('ServicesHeroShowcase.js file exists', false, error.message);
}

// Check 4: Verify API endpoint has safe serialization
console.log('\n4. Checking API endpoint safe serialization:');
try {
  const apiEndpoint = fs.readFileSync('pages/api/public/services.js', 'utf8');

  const hasSafeSerializeImport = apiEndpoint.includes("import { safeSerializeData } from '@/lib/safe-render-utils'");
  addResult('safeSerializeData import in API', hasSafeSerializeImport,
    hasSafeSerializeImport ? '' : 'Missing safeSerializeData import');

  const hasSafeDataTransform = apiEndpoint.includes('String(service?.id || \'\')') &&
                               apiEndpoint.includes('String(service?.name || \'\')');
  addResult('Safe data transformation in API', hasSafeDataTransform,
    hasSafeDataTransform ? '' : 'API not using safe String() conversion');

  const usesSafeSerialize = apiEndpoint.includes('safeSerializeData(transformedServices)');
  addResult('API uses safeSerializeData', usesSafeSerialize,
    usesSafeSerialize ? '' : 'API not using safeSerializeData()');

} catch (error) {
  addResult('API services endpoint exists', false, error.message);
}

// Check 5: Verify pricing array safety
console.log('\n5. Checking pricing array safety:');
try {
  const servicesPage = fs.readFileSync('pages/services.js', 'utf8');

  const hasSafePricingMap = servicesPage.includes('(service.pricing && Array.isArray(service.pricing) ? service.pricing : []).map(');
  addResult('Safe pricing array mapping', hasSafePricingMap,
    hasSafePricingMap ? '' : 'Pricing array not using safe mapping');

  const hasSafePricingRender = servicesPage.includes('safeRender(item?.title)') &&
                               servicesPage.includes('safeRender(item?.price)');
  addResult('Safe pricing item rendering', hasSafePricingRender,
    hasSafePricingRender ? '' : 'Pricing items not using safeRender()');

} catch (error) {
  addResult('Pricing array check failed', false, error.message);
}

// Check 6: Verify function parameter safety
console.log('\n6. Checking function parameter safety:');
try {
  const servicesPage = fs.readFileSync('pages/services.js', 'utf8');

  const hasSafeBookService = servicesPage.includes('safeRender(service?.id, \'\')');
  addResult('Safe handleBookService parameter', hasSafeBookService,
    hasSafeBookService ? '' : 'handleBookService not using safe parameter access');

  const hasSafeEditService = servicesPage.includes('safeRender(serviceId, \'\')');
  addResult('Safe handleEditService parameter', hasSafeEditService,
    hasSafeEditService ? '' : 'handleEditService not using safe parameter access');

} catch (error) {
  addResult('Function parameter safety check failed', false, error.message);
}

// Summary
console.log('\n' + '=' .repeat(70));
console.log('📊 VERIFICATION SUMMARY');
console.log('=' .repeat(70));

console.log(`\n🔍 Total Checks: ${results.totalChecks}`);
console.log(`✅ Passed: ${results.passedChecks}`);
console.log(`❌ Failed: ${results.failedChecks}`);

const successRate = ((results.passedChecks / results.totalChecks) * 100).toFixed(1);
console.log(`📈 Success Rate: ${successRate}%`);

if (results.failedChecks === 0) {
  console.log('\n🎉 ALL CHECKS PASSED! React Error #130 has been completely resolved!');
  console.log('\n✅ Services page is now safe from React rendering errors');
  console.log('✅ All object rendering has been replaced with safe primitives');
  console.log('✅ All array operations use existence checks');
  console.log('✅ All API data is properly serialized');
  console.log('✅ All function parameters are safely handled');
} else {
  console.log('\n⚠️  ISSUES FOUND - React Error #130 may still occur:');
  results.issues.forEach((issue, index) => {
    console.log(`\n${index + 1}. ${issue.check}`);
    console.log(`   Details: ${issue.details}`);
  });
}

console.log('\n🚀 Verification complete!');
