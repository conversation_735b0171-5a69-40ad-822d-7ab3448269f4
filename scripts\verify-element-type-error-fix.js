#!/usr/bin/env node

/**
 * Verification script for "Element type is invalid" error fix
 * Ocean Soul Sparkles - ServicesHeroShowcase Component
 */

import fs from 'fs';

console.log('🔍 Verifying "Element type is invalid" Error Fix');
console.log('=' .repeat(60));

const results = {
  totalChecks: 0,
  passedChecks: 0,
  failedChecks: 0,
  issues: []
};

function addResult(check, passed, details = '') {
  results.totalChecks++;
  if (passed) {
    results.passedChecks++;
    console.log(`   ✅ ${check}`);
  } else {
    results.failedChecks++;
    results.issues.push({ check, details });
    console.log(`   ❌ ${check} - ${details}`);
  }
}

// Check 1: Verify ServicesHeroShowcase has safeRender import
console.log('\n1. Checking ServicesHeroShowcase safeRender import:');
try {
  const heroShowcase = fs.readFileSync('components/ServicesHeroShowcase.js', 'utf8');
  
  const hasSafeRenderImport = heroShowcase.includes("import { safeRender } from '@/lib/safe-render-utils'");
  addResult('safeRender import present', hasSafeRenderImport,
    hasSafeRenderImport ? '' : 'Missing safeRender import');
  
} catch (error) {
  addResult('ServicesHeroShowcase.js file exists', false, error.message);
}

// Check 2: Verify safe array handling
console.log('\n2. Checking safe array handling:');
try {
  const heroShowcase = fs.readFileSync('components/ServicesHeroShowcase.js', 'utf8');
  
  const hasSafeArrayCheck = heroShowcase.includes('(services && Array.isArray(services) && services.length > 0)');
  addResult('Safe array check for showcaseServices', hasSafeArrayCheck,
    hasSafeArrayCheck ? '' : 'showcaseServices not using safe array checks');
  
} catch (error) {
  addResult('Array safety check failed', false, error.message);
}

// Check 3: Verify safe property rendering
console.log('\n3. Checking safe property rendering:');
try {
  const heroShowcase = fs.readFileSync('components/ServicesHeroShowcase.js', 'utf8');
  
  const hasSafeImageRender = heroShowcase.includes('safeRender(service?.image');
  addResult('Safe image rendering', hasSafeImageRender,
    hasSafeImageRender ? '' : 'service.image not using safeRender()');
  
  const hasSafeTitleRender = heroShowcase.includes('safeRender(service?.title');
  addResult('Safe title rendering', hasSafeTitleRender,
    hasSafeTitleRender ? '' : 'service.title not using safeRender()');
  
  const hasSafeIconRender = heroShowcase.includes('safeRender(service?.icon');
  addResult('Safe icon rendering', hasSafeIconRender,
    hasSafeIconRender ? '' : 'service.icon not using safeRender()');
  
  const hasSafeColorRender = heroShowcase.includes('safeRender(service?.color');
  addResult('Safe color rendering', hasSafeColorRender,
    hasSafeColorRender ? '' : 'service.color not using safeRender()');
  
} catch (error) {
  addResult('Property rendering check failed', false, error.message);
}

// Check 4: Verify no unsafe object rendering
console.log('\n4. Checking for unsafe object rendering:');
try {
  const heroShowcase = fs.readFileSync('components/ServicesHeroShowcase.js', 'utf8');
  
  const hasUnsafeImageRender = heroShowcase.includes('src={service.image}');
  addResult('No unsafe image rendering', !hasUnsafeImageRender,
    hasUnsafeImageRender ? 'Found unsafe service.image rendering' : '');
  
  const hasUnsafeTitleRender = heroShowcase.includes('alt={service.title}') && !heroShowcase.includes('safeRender(service?.title');
  addResult('No unsafe title rendering', !hasUnsafeTitleRender,
    hasUnsafeTitleRender ? 'Found unsafe service.title rendering' : '');
  
  const hasUnsafeColorRender = heroShowcase.includes('backgroundColor: service.color') && !heroShowcase.includes('safeRender(service?.color');
  addResult('No unsafe color rendering', !hasUnsafeColorRender,
    hasUnsafeColorRender ? 'Found unsafe service.color rendering' : '');
  
} catch (error) {
  addResult('Unsafe rendering check failed', false, error.message);
}

// Summary
console.log('\n' + '=' .repeat(60));
console.log('📊 VERIFICATION SUMMARY');
console.log('=' .repeat(60));

console.log(`\n🔍 Total Checks: ${results.totalChecks}`);
console.log(`✅ Passed: ${results.passedChecks}`);
console.log(`❌ Failed: ${results.failedChecks}`);

const successRate = ((results.passedChecks / results.totalChecks) * 100).toFixed(1);
console.log(`📈 Success Rate: ${successRate}%`);

if (results.failedChecks === 0) {
  console.log('\n🎉 ALL CHECKS PASSED! "Element type is invalid" error has been resolved!');
  console.log('\n✅ ServicesHeroShowcase component is now safe');
  console.log('✅ All object properties use safeRender()');
  console.log('✅ All array operations use safe checks');
  console.log('✅ No objects are passed as React element types');
  console.log('\n🚀 Services page should now load without React errors!');
} else {
  console.log('\n⚠️  ISSUES FOUND - "Element type is invalid" error may still occur:');
  results.issues.forEach((issue, index) => {
    console.log(`\n${index + 1}. ${issue.check}`);
    console.log(`   Details: ${issue.details}`);
  });
}

console.log('\n🔍 Verification complete!');
