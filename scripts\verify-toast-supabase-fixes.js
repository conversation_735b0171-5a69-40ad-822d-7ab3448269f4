/**
 * Verification script for Toast and Supabase client fixes
 * Tests that both React toast errors and Supabase client duplication issues are resolved
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 Verifying Toast and Supabase Client Fixes');
console.log('='.repeat(60));

let results = {
  passed: 0,
  failed: 0,
  issues: []
};

function addResult(test, passed, details = '') {
  if (passed) {
    results.passed++;
    console.log(`✅ ${test}`);
  } else {
    results.failed++;
    results.issues.push(`${test}: ${details}`);
    console.log(`❌ ${test}: ${details}`);
  }
}

// Test 1: Check that react-hot-toast is removed from package.json
console.log('\n1. Checking package.json dependencies:');
try {
  const packagePath = path.join(path.dirname(__dirname), 'package.json');
  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

  const hasReactToastify = packageContent.dependencies?.['react-toastify'];
  const hasReactHotToast = packageContent.dependencies?.['react-hot-toast'];

  addResult('react-toastify present', !!hasReactToastify);
  addResult('react-hot-toast removed', !hasReactHotToast);

} catch (error) {
  addResult('package.json readable', false, error.message);
}

// Test 2: Check that lib/notifications.js uses react-toastify
console.log('\n2. Checking toast library usage:');
try {
  const notificationsPath = path.join(path.dirname(__dirname), 'lib/notifications.js');
  const notificationsContent = fs.readFileSync(notificationsPath, 'utf8');

  const usesToastify = notificationsContent.includes("from 'react-toastify'");
  const usesHotToast = notificationsContent.includes("from 'react-hot-toast'");

  addResult('lib/notifications.js uses react-toastify', usesToastify);
  addResult('lib/notifications.js does not use react-hot-toast', !usesHotToast);

} catch (error) {
  addResult('lib/notifications.js readable', false, error.message);
}

// Test 3: Check that lib/supabase-admin.js is removed
console.log('\n3. Checking Supabase client consolidation:');
try {
  const supabaseAdminPath = path.join(path.dirname(__dirname), 'lib/supabase-admin.js');
  const supabaseAdminExists = fs.existsSync(supabaseAdminPath);

  addResult('lib/supabase-admin.js removed', !supabaseAdminExists);

} catch (error) {
  addResult('lib/supabase-admin.js check', false, error.message);
}

// Test 4: Check that main lib/supabase.js exports are correct
console.log('\n4. Checking main Supabase client exports:');
try {
  const supabasePath = path.join(path.dirname(__dirname), 'lib/supabase.js');
  const supabaseContent = fs.readFileSync(supabasePath, 'utf8');

  const exportsSupabase = supabaseContent.includes('export const supabase');
  const exportsGetAdminClient = supabaseContent.includes('export const getAdminClient');
  const exportsSupabaseAdmin = supabaseContent.includes('export const supabaseAdmin');

  addResult('exports supabase client', exportsSupabase);
  addResult('exports getAdminClient function', exportsGetAdminClient);
  addResult('exports supabaseAdmin client', exportsSupabaseAdmin);

} catch (error) {
  addResult('lib/supabase.js readable', false, error.message);
}

// Test 5: Check for remaining duplicate createClient calls
console.log('\n5. Checking for duplicate Supabase client creation:');
try {
  const problematicFiles = [];

  // Check specific files that were updated
  const filesToCheck = [
    'pages/admin/dashboard.js',
    'pages/api/admin/set-user-role.js',
    'pages/api/admin/direct-set-role.js',
    'components/admin/DataExport.js'
  ];

  filesToCheck.forEach(file => {
    try {
      const filePath = path.join(path.dirname(__dirname), file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');

        // Check if file imports from main supabase.js
        const importsFromMain = content.includes("from '@/lib/supabase'");
        const hasCreateClient = content.includes('createClient(');

        if (!importsFromMain || hasCreateClient) {
          problematicFiles.push(file);
        }
      }
    } catch (error) {
      console.warn(`Warning: Could not check ${file}: ${error.message}`);
    }
  });

  addResult('No duplicate Supabase clients in updated files', problematicFiles.length === 0,
    problematicFiles.length > 0 ? `Found in: ${problematicFiles.join(', ')}` : '');

} catch (error) {
  addResult('Duplicate client check', false, error.message);
}

// Test 6: Check _app.js toast configuration
console.log('\n6. Checking _app.js toast configuration:');
try {
  const appPath = path.join(path.dirname(__dirname), 'pages/_app.js');
  const appContent = fs.readFileSync(appPath, 'utf8');

  const importsToastify = appContent.includes("'react-toastify/dist/ReactToastify.css'");
  const hasToastContainer = appContent.includes('ToastContainer');
  const noHotToastImport = !appContent.includes("'react-hot-toast'");

  addResult('_app.js imports react-toastify CSS', importsToastify);
  addResult('_app.js has ToastContainer', hasToastContainer);
  addResult('_app.js does not import react-hot-toast', noHotToastImport);

} catch (error) {
  addResult('_app.js readable', false, error.message);
}

// Test 7: Check middleware.js for duplicate clients
console.log('\n7. Checking middleware.js:');
try {
  const middlewarePath = path.join(path.dirname(__dirname), 'middleware.js');
  if (fs.existsSync(middlewarePath)) {
    const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');

    // Middleware is allowed to create its own client for security reasons
    const hasCreateClient = middlewareContent.includes('createClient');
    addResult('middleware.js checked', true, hasCreateClient ? 'Has createClient (expected)' : 'No createClient');
  } else {
    addResult('middleware.js exists', false, 'File not found');
  }

} catch (error) {
  addResult('middleware.js check', false, error.message);
}

// Summary
console.log('\n' + '='.repeat(60));
console.log('📊 VERIFICATION SUMMARY');
console.log('='.repeat(60));

console.log(`✅ Passed: ${results.passed}`);
console.log(`❌ Failed: ${results.failed}`);
console.log(`🎯 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);

if (results.failed > 0) {
  console.log('\n⚠️  ISSUES FOUND:');
  results.issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue}`);
  });
} else {
  console.log('\n🎉 ALL CHECKS PASSED!');
  console.log('✅ Toast library conflicts resolved');
  console.log('✅ Supabase client duplication eliminated');
  console.log('✅ Services page should now load without React errors');
}

console.log('\n🔍 Verification complete!');
