/**
 * Verification script for ServicesHeroShowcase React element type error fix
 * Tests that the "Element type is invalid" error is resolved
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 Verifying ServicesHeroShowcase React Element Type Fix');
console.log('='.repeat(70));

let results = {
  passed: 0,
  failed: 0,
  issues: []
};

function addResult(test, passed, details = '') {
  if (passed) {
    results.passed++;
    console.log(`✅ ${test}`);
  } else {
    results.failed++;
    results.issues.push(`${test}: ${details}`);
    console.log(`❌ ${test}: ${details}`);
  }
}

// Test 1: Check that ServicesHeroShowcase imports safeRender
console.log('\n1. Checking ServicesHeroShowcase imports:');
try {
  const showcasePath = path.join(path.dirname(__dirname), 'components/ServicesHeroShowcase.js');
  const showcaseContent = fs.readFileSync(showcasePath, 'utf8');
  
  const importsSafeRender = showcaseContent.includes("import { safeRender } from '@/lib/safe-render-utils'");
  const importsAnimatedSection = showcaseContent.includes("import AnimatedSection from './AnimatedSection'");
  
  addResult('imports safeRender utility', importsSafeRender);
  addResult('imports AnimatedSection component', importsAnimatedSection);
  
} catch (error) {
  addResult('ServicesHeroShowcase.js readable', false, error.message);
}

// Test 2: Check safe rendering patterns in ServicesHeroShowcase
console.log('\n2. Checking safe rendering patterns:');
try {
  const showcasePath = path.join(path.dirname(__dirname), 'components/ServicesHeroShowcase.js');
  const showcaseContent = fs.readFileSync(showcasePath, 'utf8');
  
  // Check for safe array mapping
  const hasSafeArrayMapping = showcaseContent.includes('(showcaseServices && Array.isArray(showcaseServices) ? showcaseServices : [])');
  
  // Check for safe property access
  const hasSafeImageAccess = showcaseContent.includes('safeRender(service?.image');
  const hasSafeTitleAccess = showcaseContent.includes('safeRender(service?.title');
  const hasSafeIconAccess = showcaseContent.includes('safeRender(service?.icon');
  const hasSafeColorAccess = showcaseContent.includes('safeRender(service?.color');
  
  // Check for safe key prop
  const hasSafeKeyProp = showcaseContent.includes('key={safeRender(service?.id');
  
  // Check for no unsafe object rendering
  const hasUnsafeImageRendering = showcaseContent.includes('src={service.image}') && !showcaseContent.includes('safeRender(service?.image');
  const hasUnsafeTitleRendering = showcaseContent.includes('alt={service.title}') && !showcaseContent.includes('safeRender(service?.title');
  const hasUnsafeIconRendering = showcaseContent.includes('{service.icon}') && !showcaseContent.includes('safeRender(service?.icon');
  const hasUnsafeColorRendering = showcaseContent.includes('backgroundColor: service.color') && !showcaseContent.includes('safeRender(service?.color');
  
  addResult('safe array mapping for showcaseServices', hasSafeArrayMapping);
  addResult('safe image property access', hasSafeImageAccess);
  addResult('safe title property access', hasSafeTitleAccess);
  addResult('safe icon property access', hasSafeIconAccess);
  addResult('safe color property access', hasSafeColorAccess);
  addResult('safe key prop rendering', hasSafeKeyProp);
  addResult('no unsafe image rendering', !hasUnsafeImageRendering);
  addResult('no unsafe title rendering', !hasUnsafeTitleRendering);
  addResult('no unsafe icon rendering', !hasUnsafeIconRendering);
  addResult('no unsafe color rendering', !hasUnsafeColorRendering);
  
} catch (error) {
  addResult('safe rendering patterns check', false, error.message);
}

// Test 3: Check that services page still uses safe rendering
console.log('\n3. Checking services page safe rendering:');
try {
  const servicesPath = path.join(path.dirname(__dirname), 'pages/services.js');
  const servicesContent = fs.readFileSync(servicesPath, 'utf8');
  
  const importsSafeRender = servicesContent.includes("import { safeRender } from '@/lib/safe-render-utils'");
  const hasSafeHeroServicesMapping = servicesContent.includes('(services && Array.isArray(services) ? services : []).map');
  const hasSafeMainServicesMapping = servicesContent.includes('(services && Array.isArray(services) ? services : []).map');
  
  addResult('services page imports safeRender', importsSafeRender);
  addResult('services page uses safe array mapping', hasSafeHeroServicesMapping);
  
} catch (error) {
  addResult('services page check', false, error.message);
}

// Test 4: Check AnimatedSection component export
console.log('\n4. Checking AnimatedSection component:');
try {
  const animatedSectionPath = path.join(path.dirname(__dirname), 'components/AnimatedSection.js');
  const animatedSectionContent = fs.readFileSync(animatedSectionPath, 'utf8');
  
  const hasDefaultExport = animatedSectionContent.includes('export default AnimatedSection');
  const isValidComponent = animatedSectionContent.includes('const AnimatedSection = (');
  
  addResult('AnimatedSection has default export', hasDefaultExport);
  addResult('AnimatedSection is valid React component', isValidComponent);
  
} catch (error) {
  addResult('AnimatedSection component check', false, error.message);
}

// Test 5: Check for fallback values in safe rendering
console.log('\n5. Checking fallback values:');
try {
  const showcasePath = path.join(path.dirname(__dirname), 'components/ServicesHeroShowcase.js');
  const showcaseContent = fs.readFileSync(showcasePath, 'utf8');
  
  const hasImageFallback = showcaseContent.includes("'/images/services/face-paint.jpg'");
  const hasTitleFallback = showcaseContent.includes("'Service'");
  const hasIconFallback = showcaseContent.includes("'🎨'");
  const hasColorFallback = showcaseContent.includes("'#4ECDC4'");
  
  addResult('has image fallback', hasImageFallback);
  addResult('has title fallback', hasTitleFallback);
  addResult('has icon fallback', hasIconFallback);
  addResult('has color fallback', hasColorFallback);
  
} catch (error) {
  addResult('fallback values check', false, error.message);
}

// Test 6: Check API endpoint data structure
console.log('\n6. Checking API endpoint compatibility:');
try {
  const apiPath = path.join(path.dirname(__dirname), 'pages/api/public/services.js');
  const apiContent = fs.readFileSync(apiPath, 'utf8');
  
  const usesSafeSerializeData = apiContent.includes('safeSerializeData');
  const returnsProperStructure = apiContent.includes('title:') && apiContent.includes('image:');
  
  addResult('API uses safeSerializeData', usesSafeSerializeData);
  addResult('API returns proper data structure', returnsProperStructure);
  
} catch (error) {
  addResult('API endpoint check', false, error.message);
}

// Summary
console.log('\n' + '='.repeat(70));
console.log('📊 VERIFICATION SUMMARY');
console.log('='.repeat(70));

console.log(`✅ Passed: ${results.passed}`);
console.log(`❌ Failed: ${results.failed}`);
console.log(`🎯 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);

if (results.failed > 0) {
  console.log('\n⚠️  ISSUES FOUND:');
  results.issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue}`);
  });
} else {
  console.log('\n🎉 ALL CHECKS PASSED!');
  console.log('✅ ServicesHeroShowcase uses safe rendering patterns');
  console.log('✅ No unsafe object rendering detected');
  console.log('✅ Proper fallback values implemented');
  console.log('✅ React "Element type is invalid" error should be resolved');
}

console.log('\n🔍 Verification complete!');
