# Services & Shop Management - Quick Start Guide

## Overview

The admin "Services & Shop" section (formerly "Inventory") provides comprehensive management capabilities for both services and shop products. This enhanced system allows administrators to efficiently manage content, pricing, and availability across the website.

## Key Features

### ✅ Completed Enhancements

1. **Renamed Admin Section**
   - Changed from "Inventory" to "Services & Shop"
   - Updated navigation labels and page titles
   - Maintained URL structure at `/admin/inventory`

2. **Complete ServiceForm Component**
   - Full CRUD operations for services
   - Rich form fields including pricing, duration, categories
   - Image management capabilities
   - SEO optimization fields
   - Status and featured service toggles

3. **Enhanced Admin Integration**
   - Admin edit buttons on public services page (when logged in as admin)
   - Admin edit buttons on shop products (when logged in as admin)
   - Floating admin panel links on public pages
   - Direct navigation between admin and public views

4. **Improved User Experience**
   - Tabbed interface for easy navigation
   - Modal-based editing forms
   - Real-time form validation
   - Success/error feedback

## How to Use

### Accessing the Admin Panel

1. **Login as Admin**: Ensure you're logged in with admin privileges
2. **Navigate**: Go to `/admin/inventory` or click "Services & Shop" in the admin sidebar
3. **Choose Tab**: Select either "Services" or "Products" tab

### Managing Services

#### Adding a New Service
1. Click the "Services" tab
2. Click "Add Service" button
3. Fill in the service form:
   - **Basic Information**: Name, category, description
   - **Pricing & Duration**: Price (AUD), duration in minutes, calendar color
   - **Images**: Main image URL (future: file upload)
   - **Settings**: Status (active/inactive), featured toggle
4. Click "Create Service"

#### Editing Existing Services
1. **From Admin Panel**: Click "Edit" button next to any service in the list
2. **From Public Page**: When logged in as admin, click the "✏️ Edit" button on service cards
3. Update the form fields as needed
4. Click "Update Service"

### Managing Products

#### Adding a New Product
1. Click the "Products" tab
2. Click "Add Product" button
3. Fill in the product form with details like name, description, pricing, stock levels
4. Click "Create Product"

#### Editing Existing Products
1. **From Admin Panel**: Click "Edit" button next to any product in the list
2. **From Public Shop Page**: When logged in as admin, click the "✏️" button on product cards
3. Update the form fields as needed
4. Click "Update Product"

### Quick Admin Access

When logged in as an administrator, you'll see:

1. **Edit Buttons**: Directly on service and product cards on public pages
2. **Floating Admin Links**:
   - "⚙️ Manage Services" button on the services page
   - "⚙️ Manage Products" button on the shop page
3. **Direct Navigation**: Easy switching between public view and admin editing

## Database Integration

### Services Table Structure
```sql
- id (UUID, Primary Key)
- name (Text, Required)
- description (Text)
- duration (Integer, Required) -- in minutes
- price (Decimal, Required)
- color (Text) -- for calendar display
- category (Text)
- image_url (Text)
- status (Text) -- 'active' or 'inactive'
- featured (Boolean)
- meta_title (Text) -- SEO
- meta_description (Text) -- SEO
- booking_requirements (Text)
- availability_notes (Text)
- created_at (Timestamp)
- updated_at (Timestamp)
```

### Products Table Structure
```sql
- id (UUID, Primary Key)
- name (Text, Required)
- description (Text)
- sku (Text, Unique)
- price (Decimal, Required)
- sale_price (Decimal)
- cost_price (Decimal)
- category (Text)
- stock (Integer)
- low_stock_threshold (Integer)
- image_url (Text)
- gallery_images (Text Array)
- status (Text) -- 'active' or 'inactive'
- featured (Boolean)
- meta_title (Text) -- SEO
- meta_description (Text) -- SEO
- created_at (Timestamp)
- updated_at (Timestamp)
```

## API Endpoints

### Services API
- `GET /api/admin/services` - List all services
- `POST /api/admin/services` - Create new service
- `PUT /api/admin/services/{id}` - Update service
- `DELETE /api/admin/services/{id}` - Delete service

### Products API
- `GET /api/admin/inventory/products` - List all products
- `POST /api/admin/inventory/products` - Create new product
- `PUT /api/admin/inventory/products/{id}` - Update product
- `DELETE /api/admin/inventory/products/{id}` - Delete product

## Security Features

1. **Authentication Required**: All admin functions require valid admin authentication
2. **Role-Based Access**: Only users with 'admin' role can access management features
3. **Protected Routes**: Admin pages are wrapped with ProtectedRoute component
4. **API Security**: All API endpoints validate admin permissions

## Future Enhancements

### Planned Features
1. **Image Upload System**: Direct file upload instead of URL input
2. **Bulk Operations**: Edit multiple items at once
3. **Advanced Search & Filtering**: Enhanced search capabilities
4. **Analytics Integration**: Usage and performance metrics
5. **Preview Mode**: See changes before publishing
6. **Version History**: Track changes over time

### Integration Opportunities
1. **Booking System**: Direct integration with service bookings
2. **Inventory Tracking**: Real-time stock level monitoring
3. **Customer Management**: Link services to customer profiles
4. **Payment Processing**: Integrated payment handling

## Troubleshooting

### Common Issues

1. **Edit Buttons Not Showing**: Ensure you're logged in with admin privileges
2. **Form Validation Errors**: Check required fields (name, price, duration for services)
3. **Image Not Loading**: Verify image URL is accessible and valid
4. **Changes Not Saving**: Check browser console for API errors

### ✅ Recently Resolved Issues

1. **React "Element type is invalid" Error**: ✅ **RESOLVED** - Services page now uses safe rendering patterns to prevent React errors when displaying service data. All object properties are safely converted to strings before rendering.

2. **Toast Library Conflicts**: ✅ **RESOLVED** - Eliminated "TypeError: t is not a constructor" errors by standardizing on react-toastify and removing react-hot-toast conflicts.

3. **Multiple Supabase Client Instances**: ✅ **RESOLVED** - Consolidated all Supabase client creation to prevent "Multiple GoTrueClient instances detected" warnings and authentication conflicts.

### Support

For technical issues or feature requests, refer to the main admin documentation or contact the development team.

## Quick Reference

### Service Categories
- `painting` - Face & Body Painting
- `airbrush` - Airbrush Services
- `braiding` - Hair Braiding
- `glitter` - Glitter Services
- `special` - Special Events

### Product Categories
- `split-cakes` - Split Cakes
- `uv-products` - UV Products
- `glitter` - Eco-Friendly Glitter
- `kits` - Face Painting Kits
- `accessories` - Accessories

### Status Options
- `active` - Visible to customers
- `inactive` - Hidden from public view

This enhanced Services & Shop management system provides a comprehensive solution for maintaining your website's content while ensuring a seamless experience for both administrators and customers.
